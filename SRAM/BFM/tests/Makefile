# Makefile for SRAM BFM simulation
# Supports VCS and Xcelium

# --- Test Suite Selection ---
# Use 'make TEST_CASE=sram_axi' (currently only one test case)
TEST_CASE ?= sram_axi_basic

# General Settings
CXX = g++
INCLUDES_PATH = ../../src/cpp
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -I$(INCLUDES_PATH)

# Source Files
RTL_SOURCES = ../../../src/sram_axi_bfm.sv
TOP_MODULE = sram_axi_test
ifeq ($(TEST_CASE), sram_axi_basic)
	TB_SOURCES = ../../src/sv/sram_axi_basic_test.sv
else ifeq ($(TEST_CASE), sram_axi_outstanding)
	TB_SOURCES = ../../src/sv/sram_axi_outstanding_test.sv
else ifeq ($(TEST_CASE), sram_axi_overflow)
	TB_SOURCES = ../../src/sv/sram_axi_overflow_test.sv
else ifeq ($(TEST_CASE), sram_axi_chunk)
	TB_SOURCES = ../../src/sv/sram_axi_chunk_test.sv
else
TB_SOURCES = $(error Invalid TEST_CASE: $(TEST_CASE))
endif
ALL_SV_SOURCES = $(RTL_SOURCES) $(TB_SOURCES)

DPI_CPP_SOURCES = src/cpp/fake_sram_bfm.cc
SVLIB_NAME = fake_sram_bfm.so

# Output Directories
SIM_DIR = sim
VCS_DIR = $(SIM_DIR)/vcs
XLM_DIR = $(SIM_DIR)/xlm

# VCS/Xcelium Settings
VCS_HOME ?= $(shell which vcs 2>/dev/null | sed 's|/bin/vcs||')
XRUN_HOME ?= $(shell which xrun 2>/dev/null | sed 's|/bin/xrun||')

VCS_INCLUDE = $(VCS_HOME)/include
SHARED_FLAGS = -fPIC -shared -g -lpthread -DDEBUG_BFM

# Debug options
VCD = 0
ifeq ($(VCD), 1)
	DUMP_VCD = +define+DUMP_VCD
else
	DUMP_VCD =
endif

DEBUG_HW = 1
ifeq ($(DEBUG_HW), 1)
	DEBUG_HW_FLAG = +define+DEBUG_BFM
else
	DEBUG_HW_FLAG =
endif

HW_DEFINE_OPTS = $(DUMP_VCD) $(DEBUG_HW_FLAG)

# Targets
.PHONY: all clean vcs xlm vcs_comp vcs_run vcs_grun xlm_comp xlm_run xlm_grun cleanall create_dirs help

all: help

# Create simulation directories
create_dirs:
	@echo "Creating simulation directories..."
	@mkdir -p $(VCS_DIR) $(XLM_DIR)

# DPI library compilation
$(SVLIB_NAME): $(DPI_CPP_SOURCES)
	@echo "Building DPI shared library: $(SVLIB_NAME)..."
	$(CXX) $(CXXFLAGS) $(SHARED_FLAGS) -I$(VCS_INCLUDE) $(DPI_CPP_SOURCES) -o $(SVLIB_NAME)

# VCS targets
vcs: vcs_run

vcs_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for VCS..."
	cd $(VCS_DIR) && \
	vcs -full64 -sverilog -debug_access+all -timescale=1ns/1ps \
		-kdb -lca +vpi \
		$(HW_DEFINE_OPTS) \
		+incdir+../../../src \
		+incdir+../../../../src \
		$(RTL_SOURCES) \
		$(TB_SOURCES) \
		-top $(TOP_MODULE) \
		-o simv \
		-l vcs_comp.log \
		-CFLAGS "-g -I$(INCLUDES_PATH)"

vcs_run: vcs_comp
	@echo "Running VCS simulation..."
	cd $(VCS_DIR) && \
	./simv -sv_lib ../../$(basename $(SVLIB_NAME)) \
		-ucli -do ../../scripts/vsim.tcl \
		-l vcs.log

vcs_grun: vcs_comp
	@echo "Running VCS simulation with GUI..."
	cd $(VCS_DIR) && \
	./simv -gui -sv_lib ../../$(basename $(SVLIB_NAME)) \
		-ucli -do ../../scripts/vsim.tcl \
		-l vcs_gui.log

# Xcelium targets
xlm: xlm_run

xlm_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for Xcelium..."
	cd $(XLM_DIR) && \
	xrun -c -sv -access +rwc -linedebug \
		$(HW_DEFINE_OPTS) \
		+incdir+../src \
		+incdir+../../src \
		$(RTL_SOURCES) \
		$(TB_SOURCES) \
		-top $(TOP_MODULE) \
		-status -log xrun_compile.log

xlm_run: xlm_comp
	@echo "Running Xcelium simulation..."
	cd $(XLM_DIR) && \
	xrun -R -sv_lib ../$(SVLIB_NAME) \
		-input ../scripts/vsim.tcl \
		-status -l xrun_sim.log

xlm_grun: xlm_comp
	@echo "Running Xcelium simulation with GUI..."
	cd $(XLM_DIR) && \
	xrun -R -gui -sv_lib ../$(SVLIB_NAME) \
		-input ../scripts/vsim.tcl \
		-status -l xrun_gui.log

# Clean targets
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(SVLIB_NAME)
	rm -rf $(SIM_DIR)

cleanall:
	@echo "Cleaning all generated files..."
	rm -rf sim *.so

# Help target
help:
	@echo "SRAM BFM Makefile"
	@echo ""
	@echo "Usage: make [target] [TEST_CASE=sram_axi_basic]"
	@echo "Default TEST_CASE is 'sram_axi_basic'."
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Show this help message (default)"
	@echo "  $(SVLIB_NAME)    - Build the DPI-C shared library"
	@echo ""
	@echo "VCS Targets:"
	@echo "  vcs          - Compile and run simulation with VCS"
	@echo "  vcs_comp     - Compile design with VCS"
	@echo "  vcs_run      - Run simulation with VCS"
	@echo "  vcs_grun     - Run simulation with VCS and GUI"
	@echo ""
	@echo "Xcelium Targets:"
	@echo "  xlm          - Compile and run simulation with Xcelium"
	@echo "  xlm_comp     - Compile design with Xcelium"
	@echo "  xlm_run      - Run simulation with Xcelium"
	@echo "  xlm_grun     - Run simulation with Xcelium and GUI"
	@echo ""
	@echo "Management Targets:"
	@echo "  create_dirs  - Create simulation directories (sim/vcs, sim/xlm)"
	@echo "  clean        - Remove build artifacts"
	@echo "  cleanall     - Remove all generated files including sim directories"
	@echo "  help         - Show this help message"
