// Copyright (C) 2024, Advanced Micro Devices, Inc.
// SPDX-License-Identifier: MIT
//
// Author: <PERSON>

#pragma once

#include <cstdint>
#include <unordered_map>
#include <vector>
#include <queue>
#include <mutex>
#include <thread>
#include <atomic>
#include "svdpi.h"
#include <chrono>

// Forward declarations of DPI-C export functions
extern "C" {
    extern void s2h_sram_read_data_chunk(const svBitVecVal* id, const svBitVecVal* data);
}

class FakeSramBfm {
public:
    static FakeSramBfm& getInstance();
    ~FakeSramBfm();

    // DPI-C interface functions
    void init(uint16_t bfm_id, uint32_t data_width, uint32_t chunk_size_bits);
    void write_burst_first(uint16_t bfm_id, uint64_t start_addr, uint32_t burst_len, uint32_t awid, const svBitVecVal* data, const svBitVecVal* strb);
    void write_burst_next(uint16_t bfm_id, const svBitVecVal* data, const svBitVecVal* strb);
    void read_burst(uint16_t bfm_id, uint64_t start_addr, uint32_t burst_len, uint32_t arid);
    void reset();

private:
    FakeSramBfm();
    FakeSramBfm(const FakeSramBfm&) = delete;
    FakeSramBfm& operator=(const FakeSramBfm&) = delete;

    // Memory storage
    struct MemoryKey {
        uint16_t bfm_id;
        uint64_t addr;

        bool operator==(const MemoryKey& other) const {
            return bfm_id == other.bfm_id && addr == other.addr;
        }
    };

    struct MemoryKeyHash {
        size_t operator()(const MemoryKey& key) const {
            size_t h1 = std::hash<uint16_t>{}(key.bfm_id);
            size_t h2 = std::hash<uint64_t>{}(key.addr);
            return h1 ^ (h2 << 1);
        }
    };

    // Read request tracking
    struct ReadRequest {
        uint16_t bfm_id;
        uint32_t id;
        uint64_t start_addr;
        uint32_t burst_len;
        uint32_t data_size;
        uint32_t chunk_size_bits;
        svScope scope;
        bool data_ready;
        std::vector<uint8_t> data;
        std::chrono::steady_clock::time_point request_time;
    };

    std::unordered_map<MemoryKey, std::vector<uint8_t>, MemoryKeyHash> memory_;
    std::mutex memory_mutex_;

    // Write request tracking
    struct WriteBurstState {
        uint64_t current_addr;
        uint32_t beats_remaining;
    };
    std::unordered_map<uint16_t, WriteBurstState> active_writes_;
    std::mutex writes_mutex_;

    std::unordered_map<uint16_t, std::queue<ReadRequest>> pending_reads_;
    std::mutex reads_mutex_;

    std::thread data_thread_;
    std::atomic<bool> running_;

    void processDataRequests();
    void simulateDataArrival();
};