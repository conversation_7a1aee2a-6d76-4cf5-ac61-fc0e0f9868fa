#include "fake_sram_bfm.h"
#include "svdpi.h"
#include <cstring>
#include <chrono>
#include <random>
#include <unordered_map>
#include <queue>

#ifdef NO_SIMULATOR
#define vpi_printf printf
#else
#include <vpi_user.h>
#endif

std::unordered_map<uint16_t, uint32_t> bfm_sram_width_map;
std::unordered_map<uint16_t, uint32_t> bfm_chunk_size_map;

extern "C" {
    void h2s_sram_init(int unsigned bfm_id, int unsigned data_width, int unsigned chunk_size_bits) {
        FakeSramBfm::getInstance().init(bfm_id, data_width, chunk_size_bits);
    }
    void h2s_sram_write_burst_first(int unsigned bfm_id, const uint64_t* start_addr, int unsigned burst_len, const uint32_t* awid, const svBitVecVal* data, const svBitVecVal* strb) {
        FakeSramBfm::getInstance().write_burst_first(bfm_id, *start_addr, burst_len, *awid, data, strb);
    }
    
    void h2s_sram_write_burst_next(int unsigned bfm_id, const svBitVecVal* data, const svBitVecVal* strb) {
        FakeSramBfm::getInstance().write_burst_next(bfm_id, data, strb);
    }
    
    void h2s_sram_read_burst(int unsigned bfm_id, const uint64_t* start_addr, int unsigned burst_len, const uint32_t* arid) {
        FakeSramBfm::getInstance().read_burst(bfm_id, *start_addr, burst_len, *arid);
    }
}

FakeSramBfm& FakeSramBfm::getInstance() {
    static FakeSramBfm instance;
    return instance;
}

FakeSramBfm::FakeSramBfm() : running_(true) {
    data_thread_ = std::thread(&FakeSramBfm::processDataRequests, this);
}

void FakeSramBfm::init(uint16_t bfm_id, uint32_t data_width, uint32_t chunk_size_bits) {
    bfm_sram_width_map[bfm_id] = data_width;
    bfm_chunk_size_map[bfm_id] = chunk_size_bits;
    vpi_printf("[h2s_sram_init] - bfm_id: %d, data_width: %d, chunk_size_bits: %d\n", bfm_id, data_width, chunk_size_bits);
}

FakeSramBfm::~FakeSramBfm() {
    running_ = false;
    if (data_thread_.joinable()) {
        data_thread_.join();
    }
}

void FakeSramBfm::write_burst_first(uint16_t bfm_id, uint64_t start_addr, uint32_t burst_len, uint32_t awid, const svBitVecVal* data, const svBitVecVal* strb) {
    #ifdef DEBUG_BFM
    vpi_printf("[write_burst_first] - bfm_id: %d, start_addr: %lx, burst_len: %d, awid: %d\n", bfm_id, start_addr, burst_len, awid);
    #endif
    uint32_t data_width_bytes = bfm_sram_width_map[bfm_id] / 8;

    // Set up burst state
    {
        std::lock_guard<std::mutex> lock(writes_mutex_);
        active_writes_[bfm_id] = {start_addr + data_width_bytes, burst_len - 1};
    }

    // Process the first beat
    std::lock_guard<std::mutex> lock(memory_mutex_);
    MemoryKey key{bfm_id, start_addr};
    auto& mem_data = memory_[key];
    if (mem_data.empty()) {
        mem_data.resize(data_width_bytes, 0);
    }

    const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
    const uint8_t* strb_bytes = reinterpret_cast<const uint8_t*>(strb);

    for (uint32_t j = 0; j < data_width_bytes; ++j) {
        if ((strb_bytes[j / 8] >> (j % 8)) & 1) {
            mem_data[j] = data_bytes[j];
        }
    }
}

void FakeSramBfm::write_burst_next(uint16_t bfm_id, const svBitVecVal* data, const svBitVecVal* strb) {
    uint64_t current_addr;
    bool write_active = false;

    // Get current burst state
    {
        std::lock_guard<std::mutex> lock(writes_mutex_);
        auto it = active_writes_.find(bfm_id);
        if (it != active_writes_.end() && it->second.beats_remaining > 0) {
            current_addr = it->second.current_addr;
            write_active = true;
            // Update address and beat count after getting current address
            it->second.current_addr += bfm_sram_width_map[bfm_id] / 8;
            it->second.beats_remaining--;
        }
    }

    if (!write_active) {
        vpi_printf("[write_burst_next] - Error: No active burst write for bfm_id: %d\n", bfm_id);
        return;
    }

    #ifdef DEBUG_BFM
    vpi_printf("[write_burst_next] - bfm_id: %d, addr: %lx\n", bfm_id, current_addr);
    #endif

    // Process the data for the current beat
    std::lock_guard<std::mutex> lock(memory_mutex_);
    uint32_t data_width_bytes = bfm_sram_width_map[bfm_id] / 8;
    MemoryKey key{bfm_id, current_addr};
    auto& mem_data = memory_[key];
    if (mem_data.empty()) {
        mem_data.resize(data_width_bytes, 0);
    }

    const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
    const uint8_t* strb_bytes = reinterpret_cast<const uint8_t*>(strb);

    for (uint32_t j = 0; j < data_width_bytes; ++j) {
        if ((strb_bytes[j / 8] >> (j % 8)) & 1) {
            mem_data[j] = data_bytes[j];
        }
    }
}

void FakeSramBfm::read_burst(uint16_t bfm_id, uint64_t start_addr, uint32_t burst_len, uint32_t arid) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    uint32_t data_width_bytes = bfm_sram_width_map[bfm_id] / 8;

    vpi_printf("[read_burst] - bfm_id: %d, burst_len: %d, data_width_bytes: %d\n", bfm_id, burst_len, data_width_bytes);

    // Send each beat separately
    for (uint32_t i = 0; i < burst_len; ++i) {
        uint64_t current_addr = start_addr + i * data_width_bytes;
        MemoryKey key{bfm_id, current_addr};
        std::vector<uint8_t> beat_data(data_width_bytes, 0);

        auto it = memory_.find(key);
        if (it != memory_.end()) {
            memcpy(beat_data.data(), it->second.data(), data_width_bytes);
        } else {
            memset(beat_data.data(), 0, data_width_bytes);
        }

        vpi_printf("[read_burst] - sending beat: %d, addr=0x%lx\n", i, current_addr);
        s2h_sram_read_data_beat(reinterpret_cast<svBitVecVal*>(&arid), reinterpret_cast<svBitVecVal*>(beat_data.data()));
    }
}

void FakeSramBfm::processDataRequests() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dist(5, 100); // 5-100 us delay
    while (running_) {
        int sleep_us = dist(gen);
        std::this_thread::sleep_for(std::chrono::microseconds(sleep_us));
        simulateDataArrival();
    }
}

void FakeSramBfm::simulateDataArrival() {
    std::lock_guard<std::mutex> lock(reads_mutex_);
    for (auto& pair : pending_reads_) {
        auto& queue = pair.second;
        if (!queue.empty() && !queue.front().data_ready) {
            // Simulate latency - for now, just mark as ready immediately
            // A more complex model could use the request_time
            queue.front().data_ready = true;
        }
    }
}

void FakeSramBfm::reset() {
    std::lock_guard<std::mutex> mem_lock(memory_mutex_);
    std::lock_guard<std::mutex> reads_lock(reads_mutex_);
    memory_.clear();
    pending_reads_.clear();
}