`timescale 1ns/1ps

module sram_axi_test;
    // Parameters
    parameter integer AXI_DATA_WIDTH = 128;
    parameter integer AXI_ADDR_WIDTH = 20;
    parameter integer AXI_AWID_WIDTH = 4;
    parameter integer AXI_BID_WIDTH  = 4;
    parameter integer AXI_ARID_WIDTH = 4;
    parameter integer AXI_RID_WIDTH  = 4;
    parameter integer AXI_MAX_OUTSTANDING_SIZE = 16;
    parameter integer CHUNK_SIZE_BITS = 1024;

    // Clock and reset
    reg aclk;
    reg aresetn;

    // AXI Write Address Channel
    reg [AXI_AWID_WIDTH-1:0]   s_axi_awid;
    reg [AXI_ADDR_WIDTH-1:0]   s_axi_awaddr;
    reg [2:0]                  s_axi_awprot;
    reg [1:0]                  s_axi_awburst;
    reg [7:0]                  s_axi_awlen;
    reg [2:0]                  s_axi_awsize;
    reg                        s_axi_awvalid;
    wire                       s_axi_awready;

    // AXI Write Data Channel
    reg [AXI_DATA_WIDTH-1:0]   s_axi_wdata;
    reg [AXI_DATA_WIDTH/8-1:0] s_axi_wstrb;
    reg                        s_axi_wlast;
    reg                        s_axi_wvalid;
    wire                       s_axi_wready;

    // AXI Write Response Channel
    wire [AXI_BID_WIDTH-1:0]   s_axi_bid;
    wire [1:0]                 s_axi_bresp;
    wire                       s_axi_bvalid;
    reg                        s_axi_bready;

    // AXI Read Address Channel
    reg [AXI_ARID_WIDTH-1:0]   s_axi_arid;
    reg [AXI_ADDR_WIDTH-1:0]   s_axi_araddr;
    reg [2:0]                  s_axi_arprot;
    reg [1:0]                  s_axi_arburst;
    reg [7:0]                  s_axi_arlen;
    reg [2:0]                  s_axi_arsize;
    reg                        s_axi_arvalid;
    wire                       s_axi_arready;

    // AXI Read Data Channel
    wire [AXI_RID_WIDTH-1:0]   s_axi_rid;
    wire [AXI_DATA_WIDTH-1:0]  s_axi_rdata;
    wire [1:0]                 s_axi_rresp;
    wire                       s_axi_rlast;
    wire                       s_axi_rvalid;
    reg                        s_axi_rready;

    // Instantiate the BFM
    sram_axi_bfm #(
        .BFM_ID(0),
        .BLOCKING_MODE(1),
        .AXI_DATA_WIDTH(AXI_DATA_WIDTH),
        .AXI_ADDR_WIDTH(AXI_ADDR_WIDTH),
        .AXI_AWID_WIDTH(AXI_AWID_WIDTH),
        .AXI_BID_WIDTH(AXI_BID_WIDTH),
        .AXI_ARID_WIDTH(AXI_ARID_WIDTH),
        .AXI_RID_WIDTH(AXI_RID_WIDTH),
        .SRAM_BASE_ADDR(0),
        .SRAM_SIZE_BYTES(512 * 1024 * 1024),
        .READ_LATENCY_CYCLES(10),
        .AXI_MAX_OUTSTANDING_SIZE(AXI_MAX_OUTSTANDING_SIZE),
        .CHUNK_SIZE_BITS(CHUNK_SIZE_BITS)
    ) dut (
        .aclk(aclk),
        .aresetn(aresetn),
        .s_axi_awid(s_axi_awid),
        .s_axi_awaddr(s_axi_awaddr),
        .s_axi_awprot(s_axi_awprot),
        .s_axi_awburst(s_axi_awburst),
        .s_axi_awlen(s_axi_awlen),
        .s_axi_awsize(s_axi_awsize),
        .s_axi_awvalid(s_axi_awvalid),
        .s_axi_awready(s_axi_awready),
        .s_axi_wdata(s_axi_wdata),
        .s_axi_wstrb(s_axi_wstrb),
        .s_axi_wlast(s_axi_wlast),
        .s_axi_wvalid(s_axi_wvalid),
        .s_axi_wready(s_axi_wready),
        .s_axi_bid(s_axi_bid),
        .s_axi_bresp(s_axi_bresp),
        .s_axi_bvalid(s_axi_bvalid),
        .s_axi_bready(s_axi_bready),
        .s_axi_arid(s_axi_arid),
        .s_axi_araddr(s_axi_araddr),
        .s_axi_arprot(s_axi_arprot),
        .s_axi_arburst(s_axi_arburst),
        .s_axi_arlen(s_axi_arlen),
        .s_axi_arsize(s_axi_arsize),
        .s_axi_arvalid(s_axi_arvalid),
        .s_axi_arready(s_axi_arready),
        .s_axi_rid(s_axi_rid),
        .s_axi_rdata(s_axi_rdata),
        .s_axi_rresp(s_axi_rresp),
        .s_axi_rlast(s_axi_rlast),
        .s_axi_rvalid(s_axi_rvalid),
        .s_axi_rready(s_axi_rready)
    );

    // Clock generation
    initial begin
        aclk = 0;
        forever #5 aclk = ~aclk;
    end

    // Test variables
    integer outstanding_writes;
    integer outstanding_reads;
    integer write_responses_received;
    integer read_responses_received;
    integer test_errors;

    // Initialize signals
    initial begin
        aresetn = 0;
        s_axi_awid = 0;
        s_axi_awaddr = 0;
        s_axi_awprot = 0;
        s_axi_awburst = 2'b01; // INCR
        s_axi_awlen = 0;
        s_axi_awsize = 3'b100; // 16 bytes
        s_axi_awvalid = 0;
        s_axi_wdata = 0;
        s_axi_wstrb = {(AXI_DATA_WIDTH/8){1'b1}};
        s_axi_wlast = 0;
        s_axi_wvalid = 0;
        s_axi_bready = 1; // Initially ready to accept responses
        s_axi_arid = 0;
        s_axi_araddr = 0;
        s_axi_arprot = 0;
        s_axi_arburst = 2'b01; // INCR
        s_axi_arlen = 0;
        s_axi_arsize = 3'b100; // 16 bytes
        s_axi_arvalid = 0;
        s_axi_rready = 1; // Initially ready to accept data

        outstanding_writes = 0;
        outstanding_reads = 0;
        write_responses_received = 0;
        read_responses_received = 0;
        test_errors = 0;

        // Reset sequence
        #100;
        aresetn = 1;
        #50;

        $display("Test started: Reset complete.");
        
        // Test 1: Fill write FIFO to maximum capacity
        test_write_overflow();
        
        // Test 2: Fill read FIFO to maximum capacity  
        test_read_overflow();
        
        // Test 3: Mixed read/write overflow test
        test_mixed_overflow();

        #1000;
        
        if (test_errors == 0) begin
            $display("All Overflow Tests Pass");
        end else begin
            $display("Test Failed with %0d errors", test_errors);
        end
        
        $finish;
    end

    // Task to test write FIFO overflow
    task test_write_overflow();
        integer i;
        begin
            $display("Starting Write Overflow Test...");

            // Only send AW requests, no W data to prevent FIFO from draining
            s_axi_wvalid = 0;
            s_axi_bready = 1; // Keep bready high to allow responses if W data comes

            // Try to send AXI_MAX_OUTSTANDING_SIZE + 5 AW requests
            for (i = 0; i < AXI_MAX_OUTSTANDING_SIZE + 5; i++) begin
                @(posedge aclk);
                s_axi_awid = i[AXI_AWID_WIDTH-1:0];
                s_axi_awaddr = 32'h1000 + (i * 16);
                s_axi_awlen = 0; // Single beat
                s_axi_awvalid = 1;

                @(posedge aclk);
                if (s_axi_awready) begin
                    outstanding_writes++;
                    $display("AW %0d accepted, outstanding: %0d", i, outstanding_writes);
                end else begin
                    $display("AW %0d rejected (awready=%b), outstanding: %0d",
                            i, s_axi_awready, outstanding_writes);
                    if (outstanding_writes >= (AXI_MAX_OUTSTANDING_SIZE - 2)) begin
                        $display("PASS: AW FIFO correctly blocked at max outstanding (%0d)", outstanding_writes);
                    end else begin
                        $display("ERROR: AW FIFO blocked too early at %0d outstanding", outstanding_writes);
                        test_errors++;
                    end
                    break;
                end

                s_axi_awvalid = 0;
            end

            // Now send W data for all outstanding AW requests to complete the transactions
            $display("Sending W data for %0d outstanding AW requests...", outstanding_writes);
            for (i = 0; i < outstanding_writes; i++) begin
                @(posedge aclk);
                s_axi_wdata = {4{32'h00000001 + i}};
                s_axi_wstrb = {(AXI_DATA_WIDTH/8){1'b1}};
                s_axi_wlast = 1;
                s_axi_wvalid = 1;

                @(posedge aclk);
                while (!s_axi_wready) begin
                    @(posedge aclk);
                end
                s_axi_wvalid = 0;
            end

            // Wait for all responses
            while (write_responses_received < outstanding_writes) begin
                @(posedge aclk);
                if (s_axi_bvalid && s_axi_bready) begin
                    write_responses_received++;
                    $display("Write response %0d received, ID: %0d", write_responses_received, s_axi_bid);
                end
            end

            outstanding_writes = 0;
            write_responses_received = 0;
            $display("Write Overflow Test Finished.\n");
        end
    endtask

    // Task to test read FIFO overflow
    task test_read_overflow();
        integer i;
        begin
            $display("Starting Read Overflow Test...");

            // Keep read data ready high to allow responses
            s_axi_rready = 1;

            // Try to send AXI_MAX_OUTSTANDING_SIZE + 5 AR requests
            for (i = 0; i < AXI_MAX_OUTSTANDING_SIZE + 5; i++) begin
                @(posedge aclk);
                s_axi_arid = i[AXI_ARID_WIDTH-1:0];
                s_axi_araddr = 32'h1000 + (i * 16);
                s_axi_arlen = 0; // Single beat
                s_axi_arvalid = 1;

                @(posedge aclk);
                if (s_axi_arready) begin
                    outstanding_reads++;
                    $display("AR %0d accepted, outstanding: %0d", i, outstanding_reads);
                end else begin
                    $display("AR %0d rejected (arready=%b), outstanding: %0d",
                            i, s_axi_arready, outstanding_reads);
                    if (outstanding_reads >= (AXI_MAX_OUTSTANDING_SIZE - 2)) begin
                        $display("PASS: AR FIFO correctly blocked at max outstanding (%0d)", outstanding_reads);
                    end else begin
                        $display("ERROR: AR FIFO blocked too early at %0d outstanding", outstanding_reads);
                        test_errors++;
                    end
                    break;
                end

                s_axi_arvalid = 0;
            end

            // Wait for all read responses
            while (read_responses_received < outstanding_reads) begin
                @(posedge aclk);
                if (s_axi_rvalid && s_axi_rready && s_axi_rlast) begin
                    read_responses_received++;
                    $display("Read response %0d received, ID: %0d", read_responses_received, s_axi_rid);
                end
            end

            outstanding_reads = 0;
            read_responses_received = 0;
            $display("Read Overflow Test Finished.\n");
        end
    endtask

    // Task to test mixed read/write overflow
    task test_mixed_overflow();
        integer i;
        begin
            $display("Starting Mixed Read/Write Overflow Test...");
            
            // Disable both response channels
            s_axi_bready = 0;
            s_axi_rready = 0;
            
            // Interleave read and write requests
            for (i = 0; i < AXI_MAX_OUTSTANDING_SIZE; i++) begin
                // Send write request
                @(posedge aclk);
                s_axi_awid = i[AXI_AWID_WIDTH-1:0];
                s_axi_awaddr = 32'h2000 + (i * 32);
                s_axi_awlen = 0;
                s_axi_awvalid = 1;
                s_axi_wdata = {4{32'h00000010 + i}};
                s_axi_wlast = 1;
                s_axi_wvalid = 1;
                
                @(posedge aclk);
                if (s_axi_awready && s_axi_wready) begin
                    outstanding_writes++;
                end
                s_axi_awvalid = 0;
                s_axi_wvalid = 0;
                
                // Send read request
                @(posedge aclk);
                s_axi_arid = i[AXI_ARID_WIDTH-1:0];
                s_axi_araddr = 32'h2000 + (i * 32);
                s_axi_arlen = 0;
                s_axi_arvalid = 1;
                
                @(posedge aclk);
                if (s_axi_arready) begin
                    outstanding_reads++;
                end
                s_axi_arvalid = 0;
            end
            
            $display("Mixed test: %0d writes, %0d reads outstanding", outstanding_writes, outstanding_reads);
            
            // Re-enable both response channels
            s_axi_bready = 1;
            s_axi_rready = 1;
            
            // Wait for all responses
            while (write_responses_received < outstanding_writes || read_responses_received < outstanding_reads) begin
                @(posedge aclk);
                if (s_axi_bvalid && s_axi_bready) begin
                    write_responses_received++;
                end
                if (s_axi_rvalid && s_axi_rready && s_axi_rlast) begin
                    read_responses_received++;
                end
            end
            
            $display("Mixed Overflow Test Finished.\n");
        end
    endtask

endmodule
