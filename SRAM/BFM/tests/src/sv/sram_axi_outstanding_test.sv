`timescale 1ns / 1ps

module sram_axi_test;

    // Parameters
    localparam BFM_ID = 0;
    localparam BLOCKING_MODE = 1; // BFM mode is independent of AXI features
    localparam AXI_DATA_WIDTH = 128;
    localparam AXI_ADDR_WIDTH = 20;
    localparam AXI_AWID_WIDTH = 4;
    localparam AXI_BID_WIDTH = 4;
    localparam AXI_ARID_WIDTH = 4;
    localparam AXI_RID_WIDTH = 4;
    localparam SRAM_BASE_ADDR = 0;
    localparam SRAM_SIZE_BYTES = 512 * 1024 * 1024;
    localparam READ_LATENCY_CYCLES = 10;
    localparam AXI_MAX_OUTSTANDING_SIZE = 16;
    localparam NUM_TRANSACTIONS = 4;
    localparam CHUNK_SIZE_BITS = 1024;

    // Clock and Reset
    reg aclk;
    reg aresetn;

    // AXI Signals
    wire [AXI_AWID_WIDTH-1:0]   s_axi_awid;
    wire [AXI_ADDR_WIDTH-1:0]   s_axi_awaddr;
    wire [2:0]                  s_axi_awprot;
    wire [1:0]                  s_axi_awburst;
    wire [7:0]                  s_axi_awlen;
    wire [2:0]                  s_axi_awsize;
    wire                        s_axi_awvalid;
    wire                        s_axi_awready;
    wire [AXI_DATA_WIDTH-1:0]   s_axi_wdata;
    wire [AXI_DATA_WIDTH/8-1:0] s_axi_wstrb;
    wire                        s_axi_wlast;
    wire                        s_axi_wvalid;
    wire                        s_axi_wready;
    wire [AXI_BID_WIDTH-1:0]    s_axi_bid;
    wire [1:0]                  s_axi_bresp;
    wire                        s_axi_bvalid;
    wire                        s_axi_bready;
    wire [AXI_ARID_WIDTH-1:0]   s_axi_arid;
    wire [AXI_ADDR_WIDTH-1:0]   s_axi_araddr;
    wire [2:0]                  s_axi_arprot;
    wire [1:0]                  s_axi_arburst;
    wire [7:0]                  s_axi_arlen;
    wire [2:0]                  s_axi_arsize;
    wire                        s_axi_arvalid;
    wire                        s_axi_arready;
    wire [AXI_RID_WIDTH-1:0]    s_axi_rid;
    wire [AXI_DATA_WIDTH-1:0]   s_axi_rdata;
    wire [1:0]                  s_axi_rresp;
    wire                        s_axi_rlast;
    wire                        s_axi_rvalid;
    wire                        s_axi_rready;

    // Instantiate the BFM
    sram_axi_bfm #(
        .BFM_ID(BFM_ID),
        .BLOCKING_MODE(BLOCKING_MODE),
        .AXI_DATA_WIDTH(AXI_DATA_WIDTH),
        .AXI_ADDR_WIDTH(AXI_ADDR_WIDTH),
        .AXI_AWID_WIDTH(AXI_AWID_WIDTH),
        .AXI_BID_WIDTH(AXI_BID_WIDTH),
        .AXI_ARID_WIDTH(AXI_ARID_WIDTH),
        .AXI_RID_WIDTH(AXI_RID_WIDTH),
        .SRAM_BASE_ADDR(SRAM_BASE_ADDR),
        .SRAM_SIZE_BYTES(SRAM_SIZE_BYTES),
        .READ_LATENCY_CYCLES(READ_LATENCY_CYCLES),
        .AXI_MAX_OUTSTANDING_SIZE(AXI_MAX_OUTSTANDING_SIZE),
        .CHUNK_SIZE_BITS(CHUNK_SIZE_BITS)
    ) dut (
        .aclk(aclk),
        .aresetn(aresetn),
        .s_axi_awid(s_axi_awid),
        .s_axi_awaddr(s_axi_awaddr),
        .s_axi_awprot(s_axi_awprot),
        .s_axi_awburst(s_axi_awburst),
        .s_axi_awlen(s_axi_awlen),
        .s_axi_awsize(s_axi_awsize),
        .s_axi_awvalid(s_axi_awvalid),
        .s_axi_awready(s_axi_awready),
        .s_axi_wdata(s_axi_wdata),
        .s_axi_wstrb(s_axi_wstrb),
        .s_axi_wlast(s_axi_wlast),
        .s_axi_wvalid(s_axi_wvalid),
        .s_axi_wready(s_axi_wready),
        .s_axi_bid(s_axi_bid),
        .s_axi_bresp(s_axi_bresp),
        .s_axi_bvalid(s_axi_bvalid),
        .s_axi_bready(s_axi_bready),
        .s_axi_arid(s_axi_arid),
        .s_axi_araddr(s_axi_araddr),
        .s_axi_arprot(s_axi_arprot),
        .s_axi_arburst(s_axi_arburst),
        .s_axi_arlen(s_axi_arlen),
        .s_axi_arsize(s_axi_arsize),
        .s_axi_arvalid(s_axi_arvalid),
        .s_axi_arready(s_axi_arready),
        .s_axi_rid(s_axi_rid),
        .s_axi_rdata(s_axi_rdata),
        .s_axi_rresp(s_axi_rresp),
        .s_axi_rlast(s_axi_rlast),
        .s_axi_rvalid(s_axi_rvalid),
        .s_axi_rready(s_axi_rready)
    );

    // AXI Master BFM (Test Driver)
    reg [AXI_AWID_WIDTH-1:0]    m_axi_awid;
    reg [AXI_ADDR_WIDTH-1:0]    m_axi_awaddr;
    reg [1:0]                   m_axi_awburst;
    reg [7:0]                   m_axi_awlen;
    reg [2:0]                   m_axi_awsize;
    reg                         m_axi_awvalid;
    reg [AXI_DATA_WIDTH-1:0]    m_axi_wdata;
    reg [AXI_DATA_WIDTH/8-1:0]  m_axi_wstrb;
    reg                         m_axi_wlast;
    reg                         m_axi_wvalid;
    reg                         m_axi_bready;
    reg [AXI_ARID_WIDTH-1:0]    m_axi_arid;
    reg [AXI_ADDR_WIDTH-1:0]    m_axi_araddr;
    reg [1:0]                   m_axi_arburst;
    reg [7:0]                   m_axi_arlen;
    reg [2:0]                   m_axi_arsize;
    reg                         m_axi_arvalid;
    reg                         m_axi_rready;

    assign s_axi_awid = m_axi_awid;
    assign s_axi_awaddr = m_axi_awaddr;
    assign s_axi_awprot = 3'b0;
    assign s_axi_awburst = m_axi_awburst;
    assign s_axi_awlen = m_axi_awlen;
    assign s_axi_awsize = m_axi_awsize;
    assign s_axi_awvalid = m_axi_awvalid;
    assign s_axi_wdata = m_axi_wdata;
    assign s_axi_wstrb = m_axi_wstrb;
    assign s_axi_wlast = m_axi_wlast;
    assign s_axi_wvalid = m_axi_wvalid;
    assign s_axi_bready = m_axi_bready;
    assign s_axi_arid = m_axi_arid;
    assign s_axi_araddr = m_axi_araddr;
    assign s_axi_arprot = 3'b0;
    assign s_axi_arburst = m_axi_arburst;
    assign s_axi_arlen = m_axi_arlen;
    assign s_axi_arsize = m_axi_arsize;
    assign s_axi_arvalid = m_axi_arvalid;
    assign s_axi_rready = m_axi_rready;

    // Clock generation
    initial begin
        aclk = 0;
        forever #5 aclk = ~aclk;
    end

    // Test Sequence
    initial begin
        int unsigned pending_writes[int];
        int unsigned pending_reads[int];

        // 1. Reset the system
        aresetn <= 1'b0;
        m_axi_awvalid <= 1'b0;
        m_axi_wvalid  <= 1'b0;
        m_axi_bready  <= 1'b0;
        m_axi_arvalid <= 1'b0;
        m_axi_rready  <= 1'b0;
        repeat (2) @(posedge aclk);
        aresetn <= 1'b1;
        @(posedge aclk);
        $display("Test started: Reset complete.");

        // 2. Outstanding Write Test
        $display("Starting Outstanding Write Test...");
        // 1. Issue all write address requests sequentially
        begin
            for (integer i = 0; i < NUM_TRANSACTIONS; i = i + 1) begin
                m_axi_awid    <= i + 1;
                m_axi_awaddr  <= 32'h1000 * (i + 1);
                m_axi_awburst <= 2'b01; // INCR
                m_axi_awlen   <= 8'd0;  // Single beat
                m_axi_awsize  <= 3'b111; // 128-bit
                m_axi_awvalid <= 1'b1;
                pending_writes[i+1] = 1;
                @(posedge aclk);
                while (!s_axi_awready) @(posedge aclk);
                m_axi_awvalid <= 1'b0;
            end
        end

        // 2. Fork to issue write data and handle responses in parallel
        fork
            // Issue all write data requests
            begin
                for (integer i = 0; i < NUM_TRANSACTIONS; i = i + 1) begin
                    m_axi_wdata   <= {128{i+1}};
                    m_axi_wstrb   <= {AXI_DATA_WIDTH/8{1'b1}};
                    m_axi_wlast   <= 1'b1;
                    m_axi_wvalid  <= 1'b1;
                    @(posedge aclk);
                    while (!s_axi_wready) @(posedge aclk);
                    m_axi_wvalid <= 1'b0;
                end
            end

            // Wait for all write responses
            begin
                m_axi_bready <= 1'b1;
                while (pending_writes.num() > 0) begin
                    @(posedge aclk);
                    if (s_axi_bvalid) begin
                        $display("Write response ID: %h, Response: %h", s_axi_bid, s_axi_bresp);
                        if (pending_writes.exists(s_axi_bid)) begin
                            pending_writes.delete(s_axi_bid);
                        end else begin
                            $display("ERROR: Unexpected write response ID: %h", s_axi_bid);
                        end
                    end
                end
                m_axi_bready <= 1'b0;
            end
        join
        $display("Outstanding Write Test Finished.");

        repeat (2) @(posedge aclk);

        // 3. Outstanding Read Test
        $display("Starting Outstanding Read Test...");
        fork
            // Issue all read requests
            begin
                for (integer i = 0; i < NUM_TRANSACTIONS; i = i + 1) begin
                    m_axi_arid    <= i + 1;
                    m_axi_araddr  <= 32'h1000 * (i + 1);
                    m_axi_arburst <= 2'b01; // INCR
                    m_axi_arlen   <= 8'd0;  // Single beat
                    m_axi_arsize  <= 3'b111; // 128-bit
                    m_axi_arvalid <= 1'b1;
                    pending_reads[i+1] = 1;
                    @(posedge aclk);
                    while (!s_axi_arready) @(posedge aclk);
                    m_axi_arvalid <= 1'b0;
                end
            end

            // Wait for all read responses
            begin
                // Wait extra cycles for two-stage pipeline
                repeat (2) @(posedge aclk);
                m_axi_rready <= 1'b1;
                while (pending_reads.num() > 0) begin
                    @(posedge aclk);
                    if (s_axi_rvalid) begin
                        $display("Read ID: %h, Read data: %h, Last: %b", s_axi_rid, s_axi_rdata, s_axi_rlast);
                        if (pending_reads.exists(s_axi_rid)) begin
                            pending_reads.delete(s_axi_rid);
                        end else begin
                            $display("ERROR: Unexpected read response ID: %h", s_axi_rid);
                        end
                    end
                end
                m_axi_rready <= 1'b0;
            end
        join
        $display("Outstanding Read Test Finished.");

        repeat (2) @(posedge aclk);

        $finish;
    end

endmodule