`timescale 1ns / 1ps

module sram_axi_bfm #(
    parameter integer BFM_ID = 0,
    parameter bit BLOCKING_MODE = 0,
    parameter integer AXI_DATA_WIDTH = 128,
    parameter integer AXI_ADDR_WIDTH = 20,
    parameter integer AXI_AWID_WIDTH = 4,
    parameter integer AXI_BID_WIDTH  = 4,
    parameter integer AXI_ARID_WIDTH = 4,
    parameter integer AXI_RID_WIDTH  = 4,
    parameter longint unsigned SRAM_BASE_ADDR = 0,
    parameter longint unsigned SRAM_SIZE_BYTES = 512 * 1024 * 1024,
    parameter integer READ_LATENCY_CYCLES = 10,
    parameter integer AXI_MAX_OUTSTANDING_SIZE = 16,
    parameter integer CHUNK_SIZE_BITS = 2048  // Note: This version uses beat-by-beat transfer, CHUNK_SIZE_BITS is kept for compatibility
) (
    // AXI Slave Interface
    input   wire                        aclk,
    input   wire                        aresetn,

    // Write Address Channel
    input   wire [AXI_AWID_WIDTH-1:0]   s_axi_awid,
    input   wire [AXI_ADDR_WIDTH-1:0]   s_axi_awaddr,
    input   wire [2:0]                  s_axi_awprot,
    input   wire [1:0]                  s_axi_awburst,
    input   wire [7:0]                  s_axi_awlen,
    input   wire [2:0]                  s_axi_awsize,
    input   wire                        s_axi_awvalid,
    output  wire                        s_axi_awready,

    // Write Data Channel
    input   wire [AXI_DATA_WIDTH-1:0]   s_axi_wdata,
    input   wire [AXI_DATA_WIDTH/8-1:0] s_axi_wstrb,
    input   wire                        s_axi_wlast,
    input   wire                        s_axi_wvalid,
    output  wire                        s_axi_wready,

    // Write Response Channel
    output  wire [AXI_BID_WIDTH-1:0]    s_axi_bid,
    output  wire [1:0]                  s_axi_bresp,
    output  wire                        s_axi_bvalid,
    input   wire                        s_axi_bready,

    // Read Address Channel
    input   wire [AXI_ARID_WIDTH-1:0]   s_axi_arid,
    input   wire [AXI_ADDR_WIDTH-1:0]   s_axi_araddr,
    input   wire [2:0]                  s_axi_arprot,
    input   wire [1:0]                  s_axi_arburst,
    input   wire [7:0]                  s_axi_arlen,
    input   wire [2:0]                  s_axi_arsize,
    input   wire                        s_axi_arvalid,
    output  wire                        s_axi_arready,

    // Read Data Channel
    output  wire [AXI_RID_WIDTH-1:0]    s_axi_rid,
    output  wire [AXI_DATA_WIDTH-1:0]   s_axi_rdata,
    output  wire [1:0]                  s_axi_rresp,
    output  wire                        s_axi_rlast,
    output  wire                        s_axi_rvalid,
    input   wire                        s_axi_rready
);

    // DPI-C Imports
    import "DPI-C" context function void h2s_sram_init(int unsigned bfm_id, int unsigned data_width, int unsigned chunk_size_bits);
    // First beat of write burst - includes all address information
    import "DPI-C" context function void h2s_sram_write_burst_first(int unsigned bfm_id, input bit [63:0] start_addr, int unsigned burst_len, input bit [AXI_AWID_WIDTH-1:0] awid, input bit [AXI_DATA_WIDTH-1:0] data, input bit [AXI_DATA_WIDTH/8-1:0] strb);
    // Subsequent beats of write burst - only data and strobe
    import "DPI-C" context function void h2s_sram_write_burst_next(int unsigned bfm_id, input bit [AXI_DATA_WIDTH-1:0] data, input bit [AXI_DATA_WIDTH/8-1:0] strb);

    // Blocking mode read
    import "DPI-C" context function void h2s_sram_read_burst(int unsigned bfm_id, input bit [63:0] start_addr, int unsigned burst_len, input bit [AXI_ARID_WIDTH-1:0] arid);

    // Non-blocking mode read
    import "DPI-C" context function void h2s_sram_read_burst_request(int unsigned bfm_id, input bit [63:0] start_addr, int unsigned burst_len, input bit [AXI_ARID_WIDTH-1:0] arid);
    import "DPI-C" context function void h2s_sram_read_data_request(int unsigned bfm_id);


    // Internal logic signals
    // ... TODO ...

    initial begin
        h2s_sram_init(BFM_ID, AXI_DATA_WIDTH, CHUNK_SIZE_BITS);
    end

    // AXI Logic Implementation
    generate
        if (BLOCKING_MODE) begin: gen_blocking_bfm

            // Outstanding Write Address FIFO
            localparam AW_FIFO_DEPTH = AXI_MAX_OUTSTANDING_SIZE;
            typedef struct packed {
                logic [AXI_AWID_WIDTH-1:0]   id;
                logic [AXI_ADDR_WIDTH-1:0]   addr;
                logic [7:0]                  len;
                logic [2:0]                  size;
                logic [1:0]                  burst;
            } aw_info_t;
            aw_info_t                       aw_fifo[AW_FIFO_DEPTH];
            reg [$clog2(AW_FIFO_DEPTH):0]   aw_fifo_wptr;
            reg [$clog2(AW_FIFO_DEPTH):0]   aw_fifo_rptr;
            wire                            aw_fifo_full;
            wire                            aw_fifo_empty;
            logic                           aw_fifo_push;
            wire                            aw_fifo_pop;

            reg [AXI_DATA_WIDTH-1:0]    wdata_reg;
            reg [AXI_DATA_WIDTH/8-1:0]  wstrb_reg;
            reg                         wlast_reg;
            reg                         wvalid_reg;

            reg [AXI_ARID_WIDTH-1:0]    arid_reg;
            reg [AXI_ADDR_WIDTH-1:0]    araddr_reg;
            reg                         arvalid_reg;

            reg [AXI_BID_WIDTH-1:0]     bid_reg;
            reg [1:0]                   bresp_reg;
            reg                         bvalid_reg;

            reg [AXI_RID_WIDTH-1:0]     rid_reg;
            reg [AXI_DATA_WIDTH-1:0]    rdata_reg;
            reg [1:0]                   rresp_reg;
            reg                         rvalid_reg;
            reg [AXI_RID_WIDTH-1:0]     read_rid_reg;  // Register to hold the current read ID

            reg                         write_burst_active;

            assign s_axi_awready = !aw_fifo_full;
            assign s_axi_wready = !aw_fifo_empty || write_burst_active;
            assign s_axi_bid = bid_reg;
            assign s_axi_bresp = bresp_reg;
            assign s_axi_bvalid = bvalid_reg;

            // Write Logic
            reg [8:0]                           write_beat_count; // 9 bits to hold up to 256
            reg                                 write_transaction_complete;
            aw_info_t                           current_aw_info;
            aw_info_t                           head_aw_info;  // Always holds the next AW info to be processed
            reg                                 head_aw_info_valid;

            // AW FIFO count calculation
            wire [$clog2(AW_FIFO_DEPTH):0]     aw_fifo_count;

            // AW FIFO Logic
            assign aw_fifo_push = s_axi_awvalid && s_axi_awready;
            assign aw_fifo_empty = (aw_fifo_rptr == aw_fifo_wptr);

            // Calculate FIFO count considering wrap-around
            assign aw_fifo_count = (aw_fifo_wptr[$clog2(AW_FIFO_DEPTH)] == aw_fifo_rptr[$clog2(AW_FIFO_DEPTH)]) ?
                                   (aw_fifo_wptr[$clog2(AW_FIFO_DEPTH)-1:0] - aw_fifo_rptr[$clog2(AW_FIFO_DEPTH)-1:0]) :
                                   (AW_FIFO_DEPTH + aw_fifo_wptr[$clog2(AW_FIFO_DEPTH)-1:0] - aw_fifo_rptr[$clog2(AW_FIFO_DEPTH)-1:0]);

            assign aw_fifo_full = (aw_fifo_count >= AW_FIFO_DEPTH - 1);

            assign aw_fifo_pop = s_axi_bvalid && s_axi_bready;

            // Helper signal for next read pointer
            wire [$clog2(AW_FIFO_DEPTH):0] aw_fifo_rptr_next;
            assign aw_fifo_rptr_next = aw_fifo_rptr + 1;
 
            always @(posedge aclk) begin
                if (!aresetn) begin
                    aw_fifo_wptr <= '0;
                    aw_fifo_rptr <= '0;
                end else begin
                    if (aw_fifo_push) begin
                        aw_fifo[aw_fifo_wptr[$clog2(AW_FIFO_DEPTH)-1:0]] <= aw_info_t'{s_axi_awid, s_axi_awaddr, s_axi_awlen, s_axi_awsize, s_axi_awburst};
                        aw_fifo_wptr <= aw_fifo_wptr + 1;

                        // Update head_aw_info if this is the first entry or if head is not valid
                        if (!head_aw_info_valid) begin
                            head_aw_info <= aw_info_t'{s_axi_awid, s_axi_awaddr, s_axi_awlen, s_axi_awsize, s_axi_awburst};
                            head_aw_info_valid <= 1'b1;
                        end
                    end
                    if (aw_fifo_pop) begin
                        aw_fifo_rptr <= aw_fifo_rptr + 1;

                        // Update head_aw_info to next entry if FIFO is not empty after pop
                        if (aw_fifo_rptr_next != aw_fifo_wptr) begin
                            // Use next read pointer to get next aw_info
                            head_aw_info <= aw_fifo[aw_fifo_rptr_next[$clog2(AW_FIFO_DEPTH)-1:0]];
                        end else begin
                            head_aw_info_valid <= 1'b0;
                        end
                    end
                end
            end

            always @(posedge aclk) begin
                if (!aresetn) begin
                    wvalid_reg <= 1'b0;
                    bvalid_reg <= 1'b0;
                    write_burst_active <= 1'b0;
                    write_beat_count <= 9'd0;
                    bid_reg <= '0;
                    bresp_reg <= '0;
                    head_aw_info_valid <= 1'b0;
                end else begin
                    // Default assignments
                    if (bvalid_reg && s_axi_bready) begin
                        bvalid_reg <= 1'b0;
                    end

                    if (s_axi_wvalid && s_axi_wready) begin
                        if (!write_burst_active) begin
                            // Start of a new burst - use head_aw_info (no blocking assignment needed)
                            current_aw_info <= head_aw_info;
                            write_beat_count <= 1;
                            h2s_sram_write_burst_first(BFM_ID, head_aw_info.addr, head_aw_info.len + 1, head_aw_info.id, s_axi_wdata, s_axi_wstrb);

                            if (s_axi_wlast) begin // Single-beat burst
                                bvalid_reg <= 1'b1;
                                bid_reg <= head_aw_info.id;
                                bresp_reg <= 2'b00;
                            end else begin
                                write_burst_active <= 1'b1;
                            end
                        end else begin
                            // Subsequent beats of a burst
                            write_beat_count <= write_beat_count + 1;
                            h2s_sram_write_burst_next(BFM_ID, s_axi_wdata, s_axi_wstrb);

                            if (s_axi_wlast) begin
                                write_burst_active <= 1'b0;
                                bvalid_reg <= 1'b1;
                                bid_reg <= current_aw_info.id;
                                bresp_reg <= 2'b00;
                            end
                        end
                    end
                end
            end

            // Read Logic
            always @(posedge aclk) begin
                if (!aresetn) begin
                    arvalid_reg <= 1'b0;
                end else begin
                    if (s_axi_arvalid && s_axi_arready) begin
                        arvalid_reg <= 1'b1;
                    end else if (s_axi_rlast && s_axi_rready) begin
                        arvalid_reg <= 1'b0;
                    end
                end
            end
            localparam WORDS_PER_CHUNK = CHUNK_SIZE_BITS / AXI_DATA_WIDTH;
            localparam MAX_BURST_CHUNKS = 256 * AXI_DATA_WIDTH / CHUNK_SIZE_BITS;
            localparam BUFFER_DEPTH = AXI_MAX_OUTSTANDING_SIZE;
            localparam BUFFER_AWIDTH = $clog2(BUFFER_DEPTH);

            typedef struct packed {
                logic [AXI_ARID_WIDTH-1:0] id;
                logic [7:0]                len;
                logic                      active;
                logic [$clog2(256):0]      w_beat_count;
                logic [$clog2(256):0]      r_beat_count;
                logic [BUFFER_AWIDTH-1:0]  buffer_id;
            } read_txn_t;

            read_txn_t read_txns [0:BUFFER_DEPTH-1];
            reg [AXI_DATA_WIDTH*256-1:0] read_data_buffers [0:BUFFER_DEPTH-1];  // Support up to 256 beats

            reg [BUFFER_AWIDTH-1:0] ar_fifo[BUFFER_DEPTH];
            reg [BUFFER_AWIDTH:0]   ar_fifo_wptr;
            reg [BUFFER_AWIDTH:0]   ar_fifo_rptr;
            wire                    ar_fifo_full;
            wire                    ar_fifo_empty;

            reg [BUFFER_AWIDTH-1:0] r_fifo[BUFFER_DEPTH];
            reg [BUFFER_AWIDTH:0]   r_fifo_wptr;
            reg [BUFFER_AWIDTH:0]   r_fifo_rptr;
            wire                    r_fifo_full;
            wire                    r_fifo_empty;

            reg [BUFFER_AWIDTH-1:0] free_buffer_fifo[BUFFER_DEPTH];
            reg [BUFFER_AWIDTH:0]   free_buffer_wptr;
            reg [BUFFER_AWIDTH:0]   free_buffer_rptr;
            wire                    free_buffer_full;
            wire                    free_buffer_empty;

            reg [BUFFER_AWIDTH-1:0] current_read_buffer_id;
            reg                     current_read_valid;

            // Second stage pipeline for read requests
            reg                     ar_stage2_valid;
            reg [BUFFER_AWIDTH-1:0] ar_stage2_buffer_id;
            reg [AXI_ADDR_WIDTH-1:0] ar_stage2_addr;
            reg [7:0]               ar_stage2_len;
            reg [AXI_ARID_WIDTH-1:0] ar_stage2_id;

            int unsigned            buffer_idx;
            int unsigned            buffer_id;

            export "DPI-C" function s2h_sram_read_data_beat;
            function void s2h_sram_read_data_beat(input bit [AXI_ARID_WIDTH-1:0] id, input bit [AXI_DATA_WIDTH-1:0] data);
                buffer_idx = ar_stage2_buffer_id;
                $display("s2h_sram_read_data_beat, id=%0d, ar_stage2_buffer_id=%0d", id, ar_stage2_buffer_id);
                $display("s2h_sram_read_data_beat, id=%0d, data=%h, buffer_idx=%0d, txn_id=%0d", id, data, buffer_idx, read_txns[buffer_idx].id);
                if (read_txns[buffer_idx].active && read_txns[buffer_idx].id == id) begin
                    read_data_buffers[buffer_idx][read_txns[buffer_idx].w_beat_count * AXI_DATA_WIDTH +: AXI_DATA_WIDTH] = data;
                    read_txns[buffer_idx].w_beat_count = read_txns[buffer_idx].w_beat_count + 1;

                    if (read_txns[buffer_idx].w_beat_count >= read_txns[buffer_idx].len) begin
                        $display("s2h_sram_read_data_beat, id=%0d, last beat received", id);
                        r_fifo[r_fifo_wptr[BUFFER_AWIDTH-1:0]] = buffer_idx;
                        r_fifo_wptr = r_fifo_wptr + 1;
                    end
                end
            endfunction

            assign ar_fifo_empty = (ar_fifo_rptr == ar_fifo_wptr);
            assign ar_fifo_full = ((ar_fifo_wptr[BUFFER_AWIDTH-1:0] == ar_fifo_rptr[BUFFER_AWIDTH-1:0]) &&
                                   (ar_fifo_wptr[BUFFER_AWIDTH] != ar_fifo_rptr[BUFFER_AWIDTH]));
            assign r_fifo_empty = (r_fifo_rptr == r_fifo_wptr);
            assign r_fifo_full = ((r_fifo_wptr[BUFFER_AWIDTH-1:0] == r_fifo_rptr[BUFFER_AWIDTH-1:0]) &&
                                  (r_fifo_wptr[BUFFER_AWIDTH] != r_fifo_rptr[BUFFER_AWIDTH]));
            assign free_buffer_empty = (free_buffer_rptr == free_buffer_wptr);
            assign free_buffer_full = ((free_buffer_wptr[BUFFER_AWIDTH-1:0] == free_buffer_rptr[BUFFER_AWIDTH-1:0]) &&
                                       (free_buffer_wptr[BUFFER_AWIDTH] != free_buffer_rptr[BUFFER_AWIDTH]));

            assign s_axi_arready = !free_buffer_empty;
            assign s_axi_rid = rid_reg;
            assign s_axi_rdata = rdata_reg;
            assign s_axi_rresp = 2'b00; // OKAY
            assign s_axi_rvalid = rvalid_reg;
            assign s_axi_rlast = current_read_valid && (read_txns[current_read_buffer_id].r_beat_count == read_txns[current_read_buffer_id].len - 1);

            always @(posedge aclk) begin
                if (!aresetn) begin
                    ar_fifo_wptr <= '0; ar_fifo_rptr <= '0;
                    r_fifo_wptr <= '0; r_fifo_rptr <= '0;
                    free_buffer_wptr <= BUFFER_DEPTH; free_buffer_rptr <= '0;
                    for (int i = 0; i < BUFFER_DEPTH; i++) begin
                        read_txns[i].active <= 1'b0;
                        free_buffer_fifo[i] <= i;
                    end
                    current_read_valid <= 1'b0;

                    // Initialize pipeline stage2
                    ar_stage2_valid <= 1'b0;
                    ar_stage2_buffer_id <= '0;
                    ar_stage2_addr <= '0;
                    ar_stage2_len <= '0;
                    ar_stage2_id <= '0;
                end else begin
                    // First stage pipeline: Accept read request and store control info
                    if (s_axi_arvalid && s_axi_arready) begin
                        $display("Read request stage1: addr=%h, len=%d, id=%h", s_axi_araddr, s_axi_arlen, s_axi_arid);
                        buffer_id = free_buffer_fifo[free_buffer_rptr[BUFFER_AWIDTH-1:0]];
                        free_buffer_rptr <= free_buffer_rptr + 1;
                        read_txns[buffer_id] = '{id:s_axi_arid, len:s_axi_arlen+1, active:1'b1, w_beat_count:0, r_beat_count:0, buffer_id:buffer_id};
                        $display("Read request stage1: buffer_id=%0d, ar_fifo_wptr=%0d", buffer_id, ar_fifo_wptr);
                        ar_fifo[ar_fifo_wptr[BUFFER_AWIDTH-1:0]] <= buffer_id;
                        ar_fifo_wptr <= ar_fifo_wptr + 1;

                        // Pipeline stage2 control info
                        ar_stage2_valid <= 1'b1;
                        ar_stage2_buffer_id <= buffer_id;
                        ar_stage2_addr <= s_axi_araddr;
                        ar_stage2_len <= s_axi_arlen;
                        ar_stage2_id <= s_axi_arid;
                    end else begin
                        ar_stage2_valid <= 1'b0;
                    end

                    // Second stage pipeline: Issue DPI call to software side
                    if (ar_stage2_valid) begin
                        $display("Read request stage2: buffer_id=%0d, addr=%h, len=%d, id=%h",
                                ar_stage2_buffer_id, ar_stage2_addr, ar_stage2_len, ar_stage2_id);
                        h2s_sram_read_burst(BFM_ID, ar_stage2_addr, ar_stage2_len + 1, ar_stage2_id);
                    end

                    if (!current_read_valid && !r_fifo_empty) begin
                        current_read_buffer_id <= r_fifo[r_fifo_rptr[BUFFER_AWIDTH-1:0]];
                        r_fifo_rptr <= r_fifo_rptr + 1;
                        current_read_valid <= 1'b1;
                        rvalid_reg <= 1'b1;
                        // Set initial data and ID for the first beat
                        rdata_reg <= read_data_buffers[r_fifo[r_fifo_rptr[BUFFER_AWIDTH-1:0]]][0 +: AXI_DATA_WIDTH];
                        rid_reg <= read_txns[r_fifo[r_fifo_rptr[BUFFER_AWIDTH-1:0]]].id;
                    end

                    if (rvalid_reg && s_axi_rready) begin
                        $display("s_axi_rdata beat, id=%0d, beat=%0d", read_txns[current_read_buffer_id].id, read_txns[current_read_buffer_id].r_beat_count);

                        if (s_axi_rlast) begin
                            // Last beat - end transaction
                            read_txns[current_read_buffer_id].active <= 1'b0;
                            free_buffer_fifo[free_buffer_wptr[BUFFER_AWIDTH-1:0]] <= current_read_buffer_id;
                            free_buffer_wptr <= free_buffer_wptr + 1;
                            rvalid_reg <= 1'b0;
                            current_read_valid <= 1'b0;
                        end else begin
                            // Not last beat - prepare next beat
                            read_txns[current_read_buffer_id].r_beat_count <= read_txns[current_read_buffer_id].r_beat_count + 1;
                            rdata_reg <= read_data_buffers[current_read_buffer_id][(read_txns[current_read_buffer_id].r_beat_count + 1) * AXI_DATA_WIDTH +: AXI_DATA_WIDTH];
                        end
                    end
                end
            end
        end else begin: gen_nonblocking_bfm
            // implement in furture
        end
    endgenerate

endmodule
