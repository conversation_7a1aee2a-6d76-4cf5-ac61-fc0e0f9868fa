# AXI4 SRAM Mirror Memory BFM Specification

## 1. Functional Overview

This document specifies the requirements for an AXI4 SRAM Bus Functional Model (BFM). This BFM will act as an AXI4 slave and is intended for hardware/software co-simulation environments.

The BFM will support two operational modes, selectable via a parameter:
1.  **Blocking Mode:** Read operations are handled by a single DPI-C call that blocks simulation until the read data is returned from the C++ model. This mode is simpler but can impact simulation performance. This is the default operational mode.
2.  **Non-Blocking Mode:** Read operations are split into separate request and response phases, allowing the simulation to continue while the C++ model processes the request. This mode offers higher performance and supports out-of-order responses using AXI IDs.

The architecture consists of two main components:
1.  **SystemVerilog BFM:** An AXI4 slave module that provides the hardware interface. It captures AXI transactions and forwards them to a C++ model via the SystemVerilog Direct Programming Interface (DPI-C).
2.  **C++ Model:** A software model that simulates the SRAM's behavior. It manages memory storage, handles read/write requests from the SV BFM, and simulates memory access latency.

## 2. AXI4 Interface Specification

The BFM will implement a standard AXI4 slave interface.

### 2.1. Write Address Channel (AW)
| Signal | Direction | Width | Description |
|---|---|---|---|
| `s_axi_awid` | Input | `AXI_AWID_WIDTH-1:0` | Write address ID |
| `s_axi_awaddr` | Input | `AXI_ADDR_WIDTH-1:0` | Write address |
| `s_axi_awprot` | Input | `2:0` | Protection type (Not used, ignored) |
| `s_axi_awvalid` | Input | `1` | Write address valid |
| `s_axi_awready` | Output | `1` | Write address ready |
| `s_axi_awburst` | Input | `1:0` | Burst type |
| `s_axi_awlen` | Input | `7:0` | Burst length |
| `s_axi_awsize` | Input | `2:0` | Burst size |

### 2.2. Write Data Channel (W)
| Signal | Direction | Width | Description |
|---|---|---|---|
| `s_axi_wdata` | Input | `AXI_DATA_WIDTH-1:0` | Write data |
| `s_axi_wstrb` | Input | `(AXI_DATA_WIDTH/8)-1:0` | Write strobes |
| `s_axi_wlast` | Input | `1` | Last beat of a write burst |
| `s_axi_wvalid` | Input | `1` | Write data valid |
| `s_axi_wready` | Output | `1` | Write data ready |

### 2.2.1. Write Data Handling Details
For burst write transactions, each beat of the write data will trigger a separate DPI call to the software side. Note that the DPI function can only write data of `DATA_WIDTH` length in a single call, and cannot wait for `wlast` to be asserted to write the entire burst data at once.

### 2.3. Write Response Channel (B)
| Signal | Direction | Width | Description |
|---|---|---|---|
| `s_axi_bid` | Output | `AXI_BID_WIDTH-1:0` | Write response ID |
| `s_axi_bresp` | Output | `1:0` | Write response |
| `s_axi_bvalid` | Output | `1` | Write response valid |
| `s_axi_bready` | Input | `1` | Write response ready |

### 2.4. Read Address Channel (AR)
| Signal | Direction | Width | Description |
|---|---|---|---|
| `s_axi_arid` | Input | `AXI_ARID_WIDTH-1:0` | Read address ID |
| `s_axi_araddr` | Input | `AXI_ADDR_WIDTH-1:0` | Read address |
| `s_axi_arprot` | Input | `2:0` | Protection type (Not used, ignored) |
| `s_axi_arvalid` | Input | `1` | Read address valid |
| `s_axi_arready` | Output | `1` | Read address ready |
| `s_axi_arburst` | Input | `1:0` | Burst type |
| `s_axi_arlen` | Input | `7:0` | Burst length |
| `s_axi_arsize` | Input | `2:0` | Burst size |

### 2.5. Read Data Channel (R)
| Signal | Direction | Width | Description |
|---|---|---|---|
| `s_axi_rid` | Output | `AXI_RID_WIDTH-1:0` | Read ID |
| `s_axi_rdata` | Output | `AXI_DATA_WIDTH-1:0` | Read data |
| `s_axi_rresp` | Output | `1:0` | Read response |
| `s_axi_rlast` | Output | `1` | Last beat of a read burst |
| `s_axi_rvalid` | Output | `1` | Read data valid |
| `s_axi_rready` | Input | `1` | Read data ready |

### 2.5.1. Read Data Handling Details
For burst read transactions, the exported DPI function can only write one data beat to the FIFO at a time. However, on the software side, the `s2h_sram_read_data_reply` function can be called multiple times to write multiple data beats.

### 2.6. Supported Features
- **Burst Types:** The BFM shall support `FIXED`, `INCR`, and `WRAP` burst types.
- **Narrow Transfers:** The BFM shall support transactions where the burst size (`AxSIZE`) is smaller than the data bus width.
- **Unaligned Transfers:** The BFM shall support unaligned starting addresses for bursts.
- **Response Types:** The BFM shall generate `OKAY` (`2'b00`) for successful transactions and `SLVERR` (`2'b10`) for errors.

## 3. Clock and Reset
- **`aclk`**: Global clock signal. All AXI signals are synchronous to the rising edge of `aclk`.
- **`aresetn`**: Active-low asynchronous reset.

## 4. DPI-C Interface Specification

The SV BFM will communicate with the C++ model using the following DPI-C functions.

### 4.1. Imported Functions (C++ to SV)
- `void h2s_sram_init(int unsigned bfm_id, int unsigned data_width)`
  - **Description:** Initializes the C++ model for a specific BFM instance.
- `void h2s_sram_write_burst(int unsigned bfm_id, bit [63:0] start_addr, int unsigned burst_len, const svBitVecVal* awid, const svBitVecVal* data, const svBitVecVal* strb)`
  - **Description:** Handles a full write burst transaction. (Common to both modes).
- `void h2s_sram_read_burst(int unsigned bfm_id, bit [63:0] start_addr, int unsigned burst_len, const svBitVecVal* arid, output svBitVecVal* data, output svBitVecVal* rid)`
  - **Description:** **(Blocking Mode)** Handles a full read burst in a single blocking call.
- `void h2s_sram_read_burst_request(int unsigned bfm_id, bit [63:0] start_addr, int unsigned burst_len, const svBitVecVal* arid)`
  - **Description:** **(Non-Blocking Mode)** Submits a non-blocking read burst request to the C++ model.
- `void h2s_sram_read_data_request(int unsigned bfm_id)`
  - **Description:** **(Non-Blocking Mode)** Polls the C++ model to check if data for the oldest pending read is available.

### 4.2. Exported Functions (SV to C++)
- `function void s2h_sram_read_data_reply(const svBitVecVal* id, const svBitVecVal* data, bit valid)`
  - **Description:** **(Non-Blocking Mode)** The C++ model calls this function to send read data back to the SV BFM. Note that this function can only write one data beat to the FIFO at a time, but can be called multiple times by the software side to write multiple data beats for a burst transaction.

## 5. Parameterization

| Parameter | Description | Default Value |
|---|---|---|
| `BFM_ID` | Unique identifier for this SRAM instance. | `0` |
| `BLOCKING_MODE` | Selects operational mode. `1` for Blocking, `0` for Non-Blocking. | `1` |
| `AXI_DATA_WIDTH` | Width of the AXI data bus. | `128` |
| `AXI_ADDR_WIDTH` | Width of the AXI address bus. | `20` |
| `AXI_AWID_WIDTH` | Width of the write address ID bus. | `4` |
| `AXI_BID_WIDTH` | Width of the write response ID bus. | `4` |
| `AXI_ARID_WIDTH` | Width of the read address ID bus. | `4` |
| `AXI_RID_WIDTH` | Width of the read response ID bus. | `4` |
| `SRAM_BASE_ADDR` | The starting address of the SRAM in the system's memory map. | `0` |
| `SRAM_SIZE_BYTES` | Total size of the SRAM in bytes. | `536870912` (512MB) |
| `READ_LATENCY_CYCLES` | Simulated fixed read latency in clock cycles. | `10` |
| `AXI_MAX_OUTSTANDING_SIZE` | Maximum number of outstanding read transactions. | `16` |

## 6. Design Decisions

- **Default Operational Mode:** The BFM will default to **Blocking Mode** for simplicity and ease of use.
- **AXI ID Support:** The BFM will fully support AXI ID signals (`awid`, `bid`, `arid`, `rid`) to enable out-of-order transaction processing, particularly in non-blocking mode.
- **Write Response Timing:** The write response (`bvalid`) will be asserted only after the last beat of the write data (`wlast`) has been received.
- **Read Latency:** The BFM will simulate a fixed, parameterizable read latency.
- **Burst Transactions:** To maximize performance, AXI burst transactions will be passed directly to the C++ model.
- **Burst Write Handling:** For burst write transactions, each beat of the write data will trigger a separate DPI call to the software side. Note that the DPI function can only write data of `DATA_WIDTH` length in a single call, and cannot wait for `wlast` to be asserted to write the entire burst data at once.
- **Burst Read Handling:** For burst read transactions, the exported DPI function can only write one data beat to the FIFO at a time. However, on the software side, the `s2h_sram_read_data_reply` function can be called multiple times to write multiple data beats.
- **Narrow & Unaligned Transfers:** The C++ model will handle all logic for narrow and unaligned transfers.
- **Error Handling:** The BFM will generate a `SLVERR` response for out-of-bounds accesses.