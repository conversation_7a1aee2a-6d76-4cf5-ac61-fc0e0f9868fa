# Changelog

All notable changes to the SRAM BFM will be documented in this file.

## [Unreleased]

### Fixed
- Corrected DPI-C interface parameter types to comply with SystemVerilog LRM:
  - Replaced incorrect `svBitVecVal*` usage in RTL with proper `bit [N-1:0]` types
  - Updated C-side function signatures to match RTL parameter types
- Improved write burst handling:
  - Added `h2s_sram_write_burst_first` for first beat with full address information
  - Added `h2s_sram_write_burst_next` for subsequent beats with only data and strobe
- Corrected read burst data parameter sizing to properly support maximum burst lengths
- Simplified read ID handling to use single ID per burst as per AXI specification

## [v1.1] - 2024-06-15

### Added
- Implementation of burst support in blocking mode:
  - Burst write operations with per-beat DPI calls to software side
  - Burst read operations with single DPI call for all data, then beat-by-beat AXI transmission
- Test cases for burst operations in blocking mode

### Changed
- Updated specification document with implementation details for burst operations:
  - Clarified that for burst write, each beat triggers a separate DPI call to the software side
  - Clarified that for burst read, the exported DPI function writes one beat at a time, but software can call it multiple times

## [v1.0] - 2024-06-01

### Added
- Initial implementation of the SRAM BFM with both blocking and non-blocking modes
- Basic AXI4 slave interface implementation
- Blocking and non-blocking operational modes
- Support for FIXED, INCR, and WRAP burst types (planned)
- Support for narrow and unaligned transfers (planned)