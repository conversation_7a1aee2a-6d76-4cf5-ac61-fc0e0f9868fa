# AXI4 SRAM BFM - Beat-by-Beat Transfer Version

## 概述

这是AXI4 SRAM BFM的无chunk传输版本，采用beat-by-beat数据传输方式。相比chunk传输版本，这个版本具有更简单的控制逻辑和更好的带宽利用率。

## 架构特点

### 🎯 **核心设计**
- **Beat-by-Beat传输**: 每个AXI beat对应一次DPI调用
- **两级流水线**: 保持清晰的时序控制
- **简化控制逻辑**: 无需复杂的chunk计算
- **带宽优化**: 小传输不浪费带宽

### 🔧 **技术实现**

#### 1. 数据传输方式
```systemverilog
// 每个beat单独传输
export "DPI-C" function s2h_sram_read_data_beat;
function void s2h_sram_read_data_beat(
    input bit [AXI_ARID_WIDTH-1:0] id, 
    input bit [AXI_DATA_WIDTH-1:0] data  // 固定为DATA_WIDTH大小
);
```

#### 2. C++端实现
```cpp
void FakeSramBfm::read_burst(uint16_t bfm_id, uint64_t start_addr, uint32_t burst_len, uint32_t arid) {
    // 按beat发送数据
    for (uint32_t i = 0; i < burst_len; ++i) {
        uint64_t current_addr = start_addr + i * data_width_bytes;
        // 读取一个beat的数据
        std::vector<uint8_t> beat_data(data_width_bytes, 0);
        // ... 从内存读取数据
        s2h_sram_read_data_beat(arid, beat_data.data());
    }
}
```

#### 3. 两级流水线架构
```systemverilog
always @(posedge aclk) begin
    // 第一级流水：接收读请求，存储控制信息
    if (s_axi_arvalid && s_axi_arready) begin
        ar_stage2_valid <= 1'b1;
        ar_stage2_buffer_id <= buffer_id;
        ar_stage2_addr <= s_axi_araddr;
        // ...
    end
    
    // 第二级流水：发起DPI调用
    if (ar_stage2_valid) begin
        h2s_sram_read_burst(BFM_ID, ar_stage2_addr, ar_stage2_len + 1, ar_stage2_id);
    end
end
```

## 优势对比

### ✅ **相比Chunk版本的优势**

1. **控制逻辑简化**
   - 无需chunk大小计算
   - 无需chunk边界处理
   - 直接的beat计数逻辑

2. **带宽优化**
   - 单次传输：只传输16字节（128bit）
   - 小突发：按实际需要传输
   - 无带宽浪费

3. **更直观的调试**
   - 一个beat对应一次DPI调用
   - 清晰的地址映射关系
   - 简单的数据流追踪

4. **更好的实时性**
   - 每个beat立即可用
   - 无需等待整个chunk完成

### 📊 **性能对比**

| 传输类型 | Chunk版本 | Beat版本 | 带宽节省 |
|---------|-----------|----------|----------|
| 单次读取(1 beat) | 传输1024bit | 传输128bit | 87.5% |
| 小突发(4 beat) | 传输1024bit | 传输512bit | 50% |
| 大突发(32 beat) | 传输4096bit | 传输4096bit | 0% |

## 使用方法

### 1. 基本配置
```systemverilog
sram_axi_bfm #(
    .BFM_ID(0),
    .BLOCKING_MODE(0),
    .AXI_DATA_WIDTH(128),
    .AXI_MAX_OUTSTANDING_SIZE(16)
    // 注意：不需要CHUNK_SIZE_BITS参数
) dut (
    // AXI接口连接
);
```

### 2. 测试时序调整
由于采用两级流水线，测试需要额外等待一个时钟周期：
```systemverilog
// 发送读请求
m_axi_arvalid <= 1'b1;
@(posedge aclk);
while (!s_axi_arready) @(posedge aclk);
m_axi_arvalid <= 1'b0;

// 等待两级流水线稳定
@(posedge aclk);
m_axi_rready <= 1'b1;
```

## 测试验证

### ✅ **已验证的测试用例**
- **基础测试** (`sram_axi_basic`): 单次读写、突发读写
- **Outstanding测试** (`sram_axi_outstanding`): 多个并发事务
- **所有测试完全通过**

### 📈 **测试结果**
```
[read_burst] - bfm_id: 0, burst_len: 4, data_width_bytes: 16
[read_burst] - sending beat: 0, addr=0x2000
[read_burst] - sending beat: 1, addr=0x2010  
[read_burst] - sending beat: 2, addr=0x2020
[read_burst] - sending beat: 3, addr=0x2030
```

## 最佳实践

### 1. 编码规范
- 遵循Google C++ coding style
- Always块中只使用非阻塞赋值
- 保持两级流水线架构

### 2. 调试建议
- 使用调试信息追踪beat传输
- 检查地址递增是否正确
- 验证ID匹配逻辑

### 3. 性能优化
- 适用于小到中等大小的传输
- 大传输可以考虑chunk版本
- 根据应用场景选择合适版本

## 文件结构
```
SRAM/BFM/
├── src/
│   └── sram_axi_bfm.sv          # Beat-by-beat版本BFM
├── tests/
│   ├── src/
│   │   ├── cpp/
│   │   │   ├── fake_sram_bfm.cc # C++模拟模型
│   │   │   └── fake_sram_bfm.h  # 头文件
│   │   └── sv/
│   │       ├── sram_axi_basic_test.sv      # 基础测试
│   │       └── sram_axi_outstanding_test.sv # Outstanding测试
│   └── Makefile
├── best_practice.md             # 最佳实践文档
└── README.md                    # 本文档
```

## 总结

这个beat-by-beat版本的AXI4 SRAM BFM提供了：
- ✅ 简化的控制逻辑
- ✅ 优化的带宽利用
- ✅ 清晰的两级流水线架构
- ✅ 完整的测试验证
- ✅ 良好的调试支持

适合大多数SRAM访问场景，特别是小到中等大小的数据传输。
