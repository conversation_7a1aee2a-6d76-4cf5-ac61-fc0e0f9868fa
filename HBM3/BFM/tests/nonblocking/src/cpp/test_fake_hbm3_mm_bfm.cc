// test_fake_hbm3_pc.cc
#include "fake_hbm3_mm_bfm.h"
#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <vector>
#include <cstring>

// Test globals to capture DPI-C callbacks
namespace {
    std::vector<uint8_t> received_data;
    bool data_received = false;
    bool response_received = false;
}

// Implement DPI-C functions for testing
extern "C" {
    // Forward declarations from svdpi.h
    typedef void* svScope;
    
    // Stub implementations
    svScope svGetScope() {
        return nullptr;
    }

    const char* svGetNameFromScope(svScope scope) {
        return "scope";
    }
    
    svScope svSetScope(svScope scope) {
        return nullptr;
    }

    void s2h_hbm3_read_data_reply(const svBitVecVal* data, const unsigned char valid) {
        if (valid) {
            received_data.resize(32);  // 256 bits = 32 bytes
            std::memcpy(received_data.data(), data, 32);
            data_received = true;
        }
        response_received = true;
    }
}

class FakeHBM3PCTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Reset globals before each test
        received_data.clear();
        data_received = false;
        response_received = false;
        
        // Initialize test data
        test_data_.resize(32);  // 256 bits = 32 bytes
        for (size_t i = 0; i < 32; ++i) {
            test_data_[i] = i & 0xFF;
        }

        // Reset the fake HBM3PC instance
        FakeHBM3PC::getInstance().reset();

        // Initialize BFM with data width
        uint16_t bfm_id = 0;
        uint32_t data_width = 256;
        h2s_hbm3_init_bfm(reinterpret_cast<svBitVecVal*>(&bfm_id), data_width);
    }

    std::vector<uint8_t> test_data_;
};

// Basic write and read test
TEST_F(FakeHBM3PCTest, BasicWriteRead) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x1000;
    
    // Write data
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(test_data_.data()),
                reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));  // Full mask

    // Issue read request
    hbm.pcReadRequest(0, test_addr, nullptr);
    
    // Wait for data processing
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // Request data
    hbm.readRequestData(0, 16, nullptr);
    
    EXPECT_TRUE(data_received) << "Data was not received";
    EXPECT_EQ(received_data, test_data_) << "Received data does not match test data";
}

// Test data not ready scenario
TEST_F(FakeHBM3PCTest, DataNotReady) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x1000;
    
    // Write data
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(test_data_.data()),
                reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));  // Full mask

    // Only issue read request without waiting
    hbm.pcReadRequest(0, 0x1000, nullptr);
    
    //std::this_thread::sleep_for(std::chrono::microseconds(1));

    // Immediately request data
    hbm.readRequestData(0, 16, nullptr);
    
    EXPECT_TRUE(response_received) << "Response callback was not received";
    EXPECT_FALSE(data_received) << "Data should not be ready yet";

    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    hbm.readRequestData(0, 16, nullptr);
    EXPECT_TRUE(data_received) << "Data should be ready now";
    EXPECT_EQ(received_data, test_data_) << "Received data does not match test data";
}

// Test multiple reads
TEST_F(FakeHBM3PCTest, MultipleReads) {
    auto& hbm = FakeHBM3PC::getInstance();
    const int NUM_REQUESTS = 5;
    
    // Write different patterns to different addresses
    for (int i = 0; i < NUM_REQUESTS; ++i) {
        std::vector<uint8_t> pattern(32, i);  // Fill with pattern i
        hbm.pcWrite(0, 0x1000 * i,
                    reinterpret_cast<svBitVecVal*>(pattern.data()),
                    reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));
    }

    // Issue all read requests
    for (int i = 0; i < NUM_REQUESTS; ++i) {
        hbm.pcReadRequest(0, 0x1000 * i, nullptr);
    }
    
    // Wait for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // Request and verify each read
    for (int i = 0; i < NUM_REQUESTS; ++i) {
        data_received = false;
        response_received = false;
        received_data.clear();
        
        hbm.readRequestData(0, 16, nullptr);

        // Wait for data processing
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        EXPECT_TRUE(data_received) << "Data not received for request " << i;
        if (data_received) {
            std::vector<uint8_t> expected_pattern(32, i);
            EXPECT_EQ(received_data, expected_pattern) 
                << "Data mismatch for request " << i;
        }
    }
}

// Test masked writes
TEST_F(FakeHBM3PCTest, MaskedWrite) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x2000;
    
    // Write with half mask (only first 16 bytes)
    std::vector<uint8_t> half_mask = {0xFF, 0xFF, 0, 0};
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(test_data_.data()),
                reinterpret_cast<svBitVecVal*>(half_mask.data()));

    // Read back and verify
    hbm.pcReadRequest(0, test_addr, nullptr);
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    
    data_received = false;
    received_data.clear();
    
    hbm.readRequestData(0, 16, nullptr);
    
    EXPECT_TRUE(data_received);
    if (data_received) {
        // First 16 bytes should match test data
        for (size_t i = 0; i < 16; ++i) {
            EXPECT_EQ(received_data[i], test_data_[i]) 
                << "Mismatch at byte " << i;
        }
        // Last 16 bytes should be zero
        for (size_t i = 16; i < 32; ++i) {
            EXPECT_EQ(received_data[i], 0) 
                << "Non-zero at byte " << i;
        }
    }
}

// Test read consistency during interleaved writes
TEST_F(FakeHBM3PCTest, ReadConsistencyWithInterleavedWrites) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x3000;
    
    // Initial write with pattern 0xAA
    std::vector<uint8_t> initial_data(32, 0xAA);
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(initial_data.data()),
                reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));

    // Issue read request for initial data
    hbm.pcReadRequest(0, test_addr, nullptr);

    // Immediately write new pattern 0xBB (before read completes)
    std::vector<uint8_t> new_data(32, 0xBB);
    hbm.pcWrite(0, test_addr,
                reinterpret_cast<svBitVecVal*>(new_data.data()),
                reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));
    
    // Small delay to simulate processing time
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    
    // Request read data
    data_received = false;
    received_data.clear();
    hbm.readRequestData(0, 16, nullptr);
    
    EXPECT_TRUE(data_received) << "Data was not received";
    // Verify we got the initial data (0xAA) not the new data (0xBB)
    for (size_t i = 0; i < 32; ++i) {
        EXPECT_EQ(received_data[i], 0xAA) 
            << "Expected initial data (0xAA) but got different value at byte " << i;
    }

    // Issue another read request to verify new data
    hbm.pcReadRequest(0, test_addr, nullptr);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    data_received = false;
    received_data.clear();
    hbm.readRequestData(0, 16, nullptr);
    
    EXPECT_TRUE(data_received) << "Second read data was not received";
    // Verify we got the new data (0xBB)
    for (size_t i = 0; i < 32; ++i) {
        EXPECT_EQ(received_data[i], 0xBB) 
            << "Expected new data (0xBB) but got different value at byte " << i;
    }
}

// Test multiple interleaved reads and writes
TEST_F(FakeHBM3PCTest, MultipleInterleavedReadsWrites) {
    auto& hbm = FakeHBM3PC::getInstance();
    const uint64_t test_addr = 0x4000;
    const int NUM_OPERATIONS = 5;
    
    std::vector<uint8_t> expected_values;
    
    // Perform multiple interleaved reads and writes
    for (int i = 0; i < NUM_OPERATIONS; ++i) {
        // Write data pattern i
        std::vector<uint8_t> write_data(32, i);
        hbm.pcWrite(0, test_addr,
                    reinterpret_cast<svBitVecVal*>(write_data.data()),
                    reinterpret_cast<svBitVecVal*>(std::vector<uint8_t>(4, 0xFF).data()));

        // Issue read request immediately after write
        hbm.pcReadRequest(0, test_addr, nullptr);
        expected_values.push_back(i);
        
        // Small delay between operations
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
    
    // Verify all reads in order
    for (int i = 0; i < NUM_OPERATIONS; ++i) {
        data_received = false;
        received_data.clear();
        
        hbm.readRequestData(0, 16, nullptr);
        std::this_thread::sleep_for(std::chrono::microseconds(100000));
        
        EXPECT_TRUE(data_received) << "Data not received for operation " << i;
        if (data_received) {
            for (size_t j = 0; j < 32; ++j) {
                EXPECT_EQ(received_data[j], expected_values[i])
                    << "Data mismatch for operation " << i << " at byte " << j;
            }
        }
    }
}
